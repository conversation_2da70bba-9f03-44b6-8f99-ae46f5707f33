import React from 'react'
import { useTranslations } from 'next-intl'

const FaqSection = () => {
  const t = useTranslations()

  const faqs = [
    {
      id: '01',
      question: t('pricingFaq.payment.question'),
      answer: t('pricingFaq.payment.answer'),
    },
    {
      id: '02',
      question: t('pricingFaq.subscription.question'),
      answer: t('pricingFaq.subscription.answer'),
    },
    {
      id: '03',
      question: t('pricingFaq.license.question'),
      answer: t('pricingFaq.license.answer'),
    },
    {
      id: '04',
      question: t('pricingFaq.freeGen.question'),
      answer: t('pricingFaq.freeGen.answer'),
    },
    {
      id: '05',
      question: t('pricingFaq.storage.question'),
      answer: t('pricingFaq.storage.answer'),
    },
    {
      id: '06',
      question: t('pricingFaq.private.question'),
      answer: t('pricingFaq.private.answer'),
    },
  ]

  return (
    <section className="max-w-7xl mx-auto px-4 py-16">
      <div className="text-center mb-16">
        <h2 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-[#0f70e6] to-[#187dff] bg-clip-text text-transparent !leading-tight">
          {t('pricingFaq.title')}
        </h2>
        <p className="text-gray-700 text-lg md:text-xl">
          {t('pricingFaq.subtitle')}
        </p>
      </div>

      <div className="grid gap-6 max-w-4xl mx-auto">
        {faqs.map((faq) => (
          <div
            key={faq.id}
            className="bg-white rounded-xl p-6 border border-gray-200 hover:border-[#0f70e6]/30 transition-colors shadow-sm hover:shadow-md"
          >
            <h3 className="flex gap-3 text-xl font-semibold mb-4">
              <span className="text-[#0f70e6]">{faq.id}.</span>
              <span className="text-gray-900">{faq.question}</span>
            </h3>
            <p className="text-gray-700 leading-relaxed pl-9">{faq.answer}</p>
          </div>
        ))}
      </div>

      <div className="text-center mt-12 text-gray-700">
        <p>
          {t('pricingFaq.support.text')}{' '}
          <a
            href="mailto:<EMAIL>"
            className="text-[#0f70e6] hover:text-[#187dff] transition-colors"
          >
            <EMAIL>
          </a>
        </p>
      </div>

      {/* 装饰性背景元素 */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div className="absolute -top-40 -right-40 w-96 h-96 bg-[#0f70e6] opacity-5 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-20 -left-20 w-72 h-72 bg-[#187dff] opacity-5 rounded-full blur-3xl"></div>

        {/* 网格装饰 */}
        <div className="absolute inset-0 opacity-[0.02]">
          <div
            className="h-full w-full"
            style={{
              backgroundImage:
                'linear-gradient(#0f70e6 1px, transparent 1px), linear-gradient(to right, #0f70e6 1px, transparent 1px)',
              backgroundSize: '32px 32px',
            }}
          ></div>
        </div>
      </div>
    </section>
  )
}

export default FaqSection

import { ArrowRight } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { Link } from '@i18n/routing'

const HeroSection = () => {
  const t = useTranslations()

  return (
    <section className="relative flex items-center py-12 md:py-16">
      {/* 装饰性背景元素 */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div className="absolute -top-40 -right-40 w-96 h-96 bg-[#0f70e6] opacity-5 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-20 -left-20 w-72 h-72 bg-[#187dff] opacity-5 rounded-full blur-3xl"></div>

        {/* 网格装饰 */}
        <div className="absolute inset-0 opacity-[0.02]">
          <div
            className="h-full w-full"
            style={{
              backgroundImage:
                'linear-gradient(#0f70e6 1px, transparent 1px), linear-gradient(to right, #0f70e6 1px, transparent 1px)',
              backgroundSize: '32px 32px',
            }}
          ></div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 relative z-10">
        <div className="max-w-2xl">
          <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-[#0f70e6] to-[#187dff] bg-clip-text text-transparent !leading-tight">
            {t('pricingBottomArea.title')}
          </h1>

          <p className="text-lg md:text-xl text-gray-700 mb-6 leading-relaxed">
            {t('pricingBottomArea.description')}
          </p>

          <Link
            href="/ai-generate-image"
            className="group inline-flex items-center gap-2 px-6 py-3 text-base font-semibold text-white bg-[#0f70e6] hover:bg-[#187dff] rounded-full shadow-lg shadow-[#0f70e6]/30 hover:shadow-xl hover:shadow-[#0f70e6]/40 transition-all duration-200"
          >
            {t('pricingBottomArea.createButton')}
            <ArrowRight className="w-4 h-4 transform group-hover:translate-x-1 transition-transform" />
          </Link>
        </div>
      </div>
    </section>
  )
}

export default HeroSection

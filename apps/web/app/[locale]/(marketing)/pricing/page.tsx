import PricingSection from '../(home)/components/PricingSection'

import FaqSection from './components/FaqSection'
import BottomArea from './components/ButtomArea'
import { getTranslations } from 'next-intl/server'

export async function generateMetadata() {
  const t = await getTranslations()
  return {
    title: t('seo-pricing-t'),
    description: t('seo-pricing-d'),
    keywords: t('seo-pricing-k'),
  }
}

export default async function PricingPage() {
  return (
    <main className="px-8 pt-40 pb-24 bg-white text-gray-900">
      <PricingSection needTitle={false} className="leading-none" />

      <FaqSection />
      <BottomArea />
    </main>
  )
}

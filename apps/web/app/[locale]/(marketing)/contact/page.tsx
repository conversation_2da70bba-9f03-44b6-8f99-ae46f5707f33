import { Mail } from 'lucide-react'
import { getTranslations } from 'next-intl/server'

export async function generateMetadata() {
  const t = await getTranslations()
  return {
    title: t('seo-contact-t'),
    description: t('seo-contact-d'),
    keywords: t('seo-contact-k'),
  }
}

export default async function PricingPage() {
  const t = await getTranslations()

  return (
    <main className="px-8 pt-40 pb-24 relative bg-white min-h-screen text-gray-900">
      {/* 装饰性背景元素 */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div className="absolute -top-40 -right-40 w-96 h-96 bg-[#0f70e6] opacity-5 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-20 -left-20 w-72 h-72 bg-[#187dff] opacity-5 rounded-full blur-3xl"></div>

        {/* 网格装饰 */}
        <div className="absolute inset-0 opacity-[0.02]">
          <div
            className="h-full w-full"
            style={{
              backgroundImage:
                'linear-gradient(#0f70e6 1px, transparent 1px), linear-gradient(to right, #0f70e6 1px, transparent 1px)',
              backgroundSize: '32px 32px',
            }}
          ></div>
        </div>
      </div>

      <section className="py-12 md:py-16 relative z-10 mt-[240px]">
        <div className="max-w-7xl mx-auto px-4">
          <div className="max-w-2xl">
            <div className="mb-6">
              <div className="inline-block p-3 rounded-xl bg-[#0f70e6]/10 backdrop-blur-sm border border-[#0f70e6]/20 mb-4">
                <Mail className="w-6 h-6 text-[#0f70e6]" />
              </div>

              <h2 className="text-4xl font-bold mb-4 bg-gradient-to-r from-[#0f70e6] to-[#187dff] bg-clip-text text-transparent">
                {t('contact.title')}
              </h2>

              <p className="text-lg text-gray-700 leading-relaxed mb-8">
                {t('contact.description')}
              </p>
            </div>

            <div className="space-x-4 flex items-center">
              <h3 className="text-gray-700">{t('contact.email.label')}</h3>
              <a
                href="mailto:<EMAIL>"
                className="text-lg text-gray-900 hover:text-[#0f70e6] transition-colors font-medium"
              >
                <EMAIL>
              </a>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}

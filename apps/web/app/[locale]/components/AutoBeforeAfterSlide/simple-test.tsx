'use client'
import { useState } from 'react'
import AutoBeforeAfterSlider from './index'

// 简单测试组件，专门测试 loop=false, reverse=true
export default function SimpleTestComponent() {
  const [key, setKey] = useState(0)

  const resetAnimation = () => {
    setKey((prev) => prev + 1)
  }

  return (
    <div className="p-8 space-y-8">
      <h1 className="text-2xl font-bold">测试 loop=false, reverse=true</h1>

      <button
        onClick={resetAnimation}
        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        重置动画
      </button>

      <div className="space-y-4">
        <h2 className="text-lg font-semibold">
          预期行为：从右边开始，向左移动，到达左边后停止
        </h2>

        <div className="w-96 h-64 border border-gray-300">
          <AutoBeforeAfterSlider
            key={key}
            beforeImage="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0iIzMzNzNkYyIvPjx0ZXh0IHg9IjIwMCIgeT0iMTUwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMjQiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+QkVGT1JFPC90ZXh0Pjwvc3ZnPg=="
            afterImage="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0iIzEwYjk4MSIvPjx0ZXh0IHg9IjIwMCIgeT0iMTUwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMjQiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+QUZURVI8L3RleHQ+PC9zdmc+"
            speed={2}
            loop={false}
            reverse={true}
            hideBlur={true}
          />
        </div>

        <div className="text-sm text-gray-600">
          如果工作正常，你应该看到：
          <ul className="list-disc list-inside mt-2">
            <li>
              初始状态：完全显示 AFTER 图片（绿色）- 因为
              reverse=true，滑块从右边开始
            </li>
            <li>动画开始：滑块从右向左移动，逐渐显示 BEFORE 图片（蓝色）</li>
            <li>动画结束：完全显示 BEFORE 图片，并停止在左边</li>
          </ul>
        </div>

        <div className="text-xs text-gray-500 mt-4">
          注意：reverse=true 时，初始状态是显示 AFTER 图片，然后向左扫描显示
          BEFORE 图片
        </div>
      </div>
    </div>
  )
}

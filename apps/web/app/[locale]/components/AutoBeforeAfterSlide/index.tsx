'use client'
import { useState, useRef, useEffect } from 'react'
import { useTranslations } from 'next-intl'
import './shine-effects.css'

// 星星闪烁特效组件
const ShineEffect = ({ sliderPosition }: { sliderPosition: number }) => {
  const [stars, setStars] = useState<
    Array<{
      id: number
      x: number
      y: number
      delay: number
      size: number
      type: 'star' | 'sparkle'
    }>
  >([])

  useEffect(() => {
    // 生成随机星星和闪光点
    const generateStars = () => {
      const newStars = Array.from({ length: 12 }, (_, i) => ({
        id: i,
        x: Math.random() * 120 - 10, // 相对于滑块线的位置，稍微扩大范围
        y: Math.random() * 100,
        delay: Math.random() * 3, // 动画延迟
        size: Math.random() * 0.6 + 0.4, // 星星大小
        type: Math.random() > 0.6 ? 'sparkle' : ('star' as 'star' | 'sparkle'), // 随机类型
      }))
      setStars(newStars)
    }

    generateStars()
    // 每隔一段时间重新生成星星位置
    const interval = setInterval(generateStars, 4000)
    return () => clearInterval(interval)
  }, [])

  return (
    <div className="absolute inset-0 pointer-events-none overflow-hidden">
      {stars.map((star) => (
        <div
          key={star.id}
          className={`absolute ${
            star.type === 'sparkle' ? 'animate-star-trail' : 'animate-twinkle'
          }`}
          style={{
            left: `${sliderPosition + (star.x - 50) * 0.3}%`, // 围绕滑块线分布
            top: `${star.y}%`,
            animationDelay: `${star.delay}s`,
            animationDuration: star.type === 'sparkle' ? '2.5s' : '2s',
            transform: `scale(${star.size})`,
          }}
        >
          {star.type === 'star' ? (
            /* 星星 SVG */
            <svg
              width="14"
              height="14"
              viewBox="0 0 24 24"
              fill="none"
              className="text-yellow-300 drop-shadow-lg"
            >
              <path
                d="M12 2L14.09 8.26L22 9L16 14.74L17.18 22.02L12 18.77L6.82 22.02L8 14.74L2 9L9.91 8.26L12 2Z"
                fill="currentColor"
              />
            </svg>
          ) : (
            /* 闪光点 */
            <div className="relative">
              <div className="w-2 h-2 bg-white rounded-full shadow-lg"></div>
              <div className="absolute inset-0 w-2 h-2 bg-yellow-200 rounded-full animate-ping"></div>
            </div>
          )}
        </div>
      ))}

      {/* 滑块线上的光效 */}
      <div
        className="absolute top-0 bottom-0 w-4 pointer-events-none"
        style={{
          left: `${sliderPosition}%`,
          transform: 'translateX(-50%)',
        }}
      >
        {/* 主光效 */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-yellow-300/40 to-transparent animate-shine-pulse" />
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulse" />

        {/* 光线扫过效果 */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-yellow-100/60 to-transparent animate-light-sweep" />

        {/* 中心亮线 */}
        <div className="absolute top-0 bottom-0 left-1/2 w-0.5 bg-gradient-to-b from-transparent via-yellow-200 to-transparent transform -translate-x-1/2 animate-pulse" />
      </div>

      {/* 滑块线周围的光晕 */}
      <div
        className="absolute top-1/2 w-8 h-8 pointer-events-none"
        style={{
          left: `${sliderPosition}%`,
          transform: 'translate(-50%, -50%)',
        }}
      >
        <div className="absolute inset-0 bg-yellow-300/20 rounded-full animate-ping" />
        <div className="absolute inset-2 bg-white/30 rounded-full animate-pulse" />
      </div>
    </div>
  )
}

interface AutoBeforeAfterSliderProps {
  beforeImage: string
  afterImage: string
  className?: string
  beforeAlt?: string
  afterAlt?: string
  containerClassName?: string
  hideBlur?: boolean
  speed?: number // 扫过速度，单位为秒，默认为 3 秒
  pauseOnHover?: boolean // 鼠标悬停时是否暂停动画，默认为 true
  hideLabel?: boolean
  showShine?: boolean // 是否显示星星闪烁特效，默认为 false
  loop?: boolean // 是否循环动画，默认为 true。如果为 false，则在第一次从左到右扫过后停止并停留在最后一帧
}

export default function AutoBeforeAfterSlider({
  beforeImage,
  afterImage,
  className = '',
  beforeAlt = 'before',
  afterAlt = 'after',
  containerClassName = '',
  hideBlur = false,
  speed = 1.5,
  pauseOnHover = false,
  hideLabel = false,
  showShine = false,
  loop = true,
}: AutoBeforeAfterSliderProps) {
  const t = useTranslations('home')
  const [sliderPosition, setSliderPosition] = useState(0)
  const [isHovering, setIsHovering] = useState(false)
  const [direction, setDirection] = useState(1) // 1 为向右，-1 为向左
  const [isAnimationComplete, setIsAnimationComplete] = useState(false) // 跟踪动画是否已完成（仅在 loop=false 时使用）
  const containerRef = useRef<HTMLDivElement>(null)
  const animationRef = useRef<number>(0)
  const lastTimeRef = useRef<number>(0)

  const handleMouseEnter = () => {
    setIsHovering(true)
  }

  const handleMouseLeave = () => {
    setIsHovering(false)
  }

  // 动画循环函数
  const animate = (currentTime: number) => {
    if (!lastTimeRef.current) {
      lastTimeRef.current = currentTime
    }

    const deltaTime = currentTime - lastTimeRef.current
    lastTimeRef.current = currentTime

    // 如果动画已完成（仅在 loop=false 时），则停止动画
    if (!loop && isAnimationComplete) {
      return
    }

    // 如果鼠标悬停且启用了暂停功能，则不更新位置
    if (pauseOnHover && isHovering) {
      animationRef.current = requestAnimationFrame(animate)
      return
    }

    // 计算每毫秒移动的百分比
    const movePerMs = (100 / (speed * 1000)) * direction

    setSliderPosition((prevPosition) => {
      let newPosition = prevPosition + movePerMs * deltaTime

      // 检查边界并改变方向
      if (newPosition >= 100) {
        newPosition = 100
        if (loop) {
          setDirection(-1)
        } else {
          // 如果不循环，则在到达右边界时停止动画
          setIsAnimationComplete(true)
        }
      } else if (newPosition <= 0) {
        newPosition = 0
        setDirection(1)
      }

      return newPosition
    })

    animationRef.current = requestAnimationFrame(animate)
  }

  useEffect(() => {
    animationRef.current = requestAnimationFrame(animate)

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [speed, direction, isHovering, pauseOnHover, loop, isAnimationComplete])

  return (
    <div
      ref={containerRef}
      className={`relative w-full h-full border border-solid border-white/5 rounded-lg select-none ${className}`}
      style={{ touchAction: 'none' }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Container for both images */}
      <div
        className={`relative w-full overflow-hidden h-full rounded-lg ${containerClassName}`}
      >
        {/* After image (full width) with frosted glass effect background */}
        <div className="absolute inset-0 h-full">
          {/* Frosted glass background */}
          {!hideBlur && (
            <>
              <div className="absolute inset-0 bg-gradient-to-br from-gray-100/30 to-white/50 backdrop-blur-xl" />

              <div
                className="absolute inset-0 backdrop-blur-sm"
                style={{
                  backgroundImage: `linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
                              linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
                              linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
                              linear-gradient(-45deg, transparent 75%, #f0f0f0 75%)`,
                  backgroundSize: '20px 20px',
                  backgroundPosition: '0 0, 0 10px, 10px -10px, -10px 0px',
                  opacity: 0.1,
                }}
              />
            </>
          )}

          <img
            src={afterImage}
            alt={afterAlt}
            className="relative w-full h-full object-cover"
            draggable="false"
          />
        </div>

        {/* Before image (clipped) */}
        <div
          className="absolute inset-0"
          style={{
            clipPath: `inset(0 ${100 - sliderPosition}% 0 0)`,
          }}
        >
          <img
            src={beforeImage}
            alt={beforeAlt}
            className="w-full h-full object-cover"
            draggable="false"
          />
        </div>

        {/* 星星闪烁特效 */}
        {showShine && <ShineEffect sliderPosition={sliderPosition} />}

        {/* Labels */}
        {!hideLabel && (
          <>
            <div className="absolute bottom-4 left-4 px-3 py-1.5 bg-white/20 backdrop-blur text-black/30 text-sm font-medium rounded-full shadow-sm">
              {t('before')}
            </div>
            <div className="absolute bottom-4 right-4 px-3 py-1.5 bg-white/20 backdrop-blur text-black/30 text-sm font-medium rounded-full shadow-sm">
              {t('after')}
            </div>
          </>
        )}
      </div>
    </div>
  )
}

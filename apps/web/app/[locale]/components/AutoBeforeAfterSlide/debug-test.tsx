'use client'
import { useState } from 'react'
import AutoBeforeAfterSlider from './index'

// 调试测试组件
export default function DebugTestComponent() {
  const [reverse, setReverse] = useState(false)
  const [loop, setLoop] = useState(false)

  return (
    <div className="p-8 space-y-8">
      <div className="space-y-4">
        <h1 className="text-2xl font-bold">AutoBeforeAfterSlider 调试测试</h1>
        
        <div className="flex gap-4">
          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={loop}
              onChange={(e) => setLoop(e.target.checked)}
            />
            Loop
          </label>
          
          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={reverse}
              onChange={(e) => setReverse(e.target.checked)}
            />
            Reverse
          </label>
        </div>
        
        <div className="text-sm text-gray-600">
          当前配置: loop={loop.toString()}, reverse={reverse.toString()}
        </div>
        
        <div className="text-sm text-gray-600">
          预期行为: 
          {loop && !reverse && "从左到右循环"}
          {loop && reverse && "从右到左循环"}
          {!loop && !reverse && "从左到右一次，停在右边"}
          {!loop && reverse && "从右到左一次，停在左边"}
        </div>
      </div>
      
      <div className="w-96 h-64 border-2 border-dashed border-gray-300">
        <AutoBeforeAfterSlider
          beforeImage="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0iIzMzNzNkYyIvPjx0ZXh0IHg9IjIwMCIgeT0iMTUwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMjQiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+QkVGT1JFPC90ZXh0Pjwvc3ZnPg=="
          afterImage="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0iIzEwYjk4MSIvPjx0ZXh0IHg9IjIwMCIgeT0iMTUwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMjQiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+QUZURVI8L3RleHQ+PC9zdmc+"
          speed={2}
          loop={loop}
          reverse={reverse}
          hideBlur={true}
        />
      </div>
      
      <div className="text-xs text-gray-500">
        提示：切换复选框来测试不同的配置组合
      </div>
    </div>
  )
}

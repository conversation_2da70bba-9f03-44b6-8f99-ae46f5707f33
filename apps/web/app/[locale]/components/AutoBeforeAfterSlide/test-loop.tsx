'use client'
import AutoBeforeAfterSlider from './index'

// 测试组件，用于验证 loop 和 reverse 属性的功能
export default function TestLoopComponent() {
  return (
    <div className="p-8 space-y-8">
      <div>
        <h2 className="text-xl font-bold mb-4">
          默认行为 (loop=true, reverse=false)
        </h2>
        <div className="w-96 h-64">
          <AutoBeforeAfterSlider
            beforeImage="/test-before.jpg"
            afterImage="/test-after.jpg"
            speed={2}
            loop={true}
            reverse={false}
          />
        </div>
      </div>

      <div>
        <h2 className="text-xl font-bold mb-4">
          反向循环 (loop=true, reverse=true)
        </h2>
        <div className="w-96 h-64">
          <AutoBeforeAfterSlider
            beforeImage="/test-before.jpg"
            afterImage="/test-after.jpg"
            speed={2}
            loop={true}
            reverse={true}
          />
        </div>
      </div>

      <div>
        <h2 className="text-xl font-bold mb-4">
          单次正向 (loop=false, reverse=false)
        </h2>
        <div className="w-96 h-64">
          <AutoBeforeAfterSlider
            beforeImage="/test-before.jpg"
            afterImage="/test-after.jpg"
            speed={2}
            loop={false}
            reverse={false}
          />
        </div>
      </div>

      <div>
        <h2 className="text-xl font-bold mb-4">
          单次反向 (loop=false, reverse=true)
        </h2>
        <div className="w-96 h-64">
          <AutoBeforeAfterSlider
            beforeImage="/test-before.jpg"
            afterImage="/test-after.jpg"
            speed={2}
            loop={false}
            reverse={true}
          />
        </div>
      </div>
    </div>
  )
}

'use client'
import AutoBeforeAfterSlider from './index'

// 测试组件，用于验证 loop 属性的功能
export default function TestLoopComponent() {
  return (
    <div className="p-8 space-y-8">
      <div>
        <h2 className="text-xl font-bold mb-4">Loop = true (默认行为)</h2>
        <div className="w-96 h-64">
          <AutoBeforeAfterSlider
            beforeImage="/test-before.jpg"
            afterImage="/test-after.jpg"
            speed={2}
            loop={true}
          />
        </div>
      </div>
      
      <div>
        <h2 className="text-xl font-bold mb-4">Loop = false (停止在最后一帧)</h2>
        <div className="w-96 h-64">
          <AutoBeforeAfterSlider
            beforeImage="/test-before.jpg"
            afterImage="/test-after.jpg"
            speed={2}
            loop={false}
          />
        </div>
      </div>
    </div>
  )
}
